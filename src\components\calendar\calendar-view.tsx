"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { 
  Plus, 
  Calendar as CalendarIcon, 
  Clock, 
  MapPin, 
  Users, 
  Video,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  Settings,
  Search,
  TrendingUp,
  Sparkles,
  Bell,
  Filter,
  ChevronDown,
  Zap,
  BarChart3,
  Target,
  MoreHorizontal
} from "lucide-react"
import { format, isSameDay, startOfMonth, endOfMont<PERSON>, isToday, add<PERSON>ont<PERSON>, subMonths, parseISO, startOfWeek, endOfWeek, eachDayOfInterval, isSameMonth, addDays, startOfDay } from "date-fns"
import { useSession } from "next-auth/react"
import { GoogleCalendarEvent, CalendarStats } from "@/lib/calendar-types"

interface CalendarData {
  id: string
  summary: string
  description?: string
  primary?: boolean
  accessRole: string
  selected?: boolean
  backgroundColor?: string
  foregroundColor?: string
}

interface CalendarColors {
  calendar: Record<string, { background: string; foreground: string }>
  event: Record<string, { background: string; foreground: string }>
}

interface CalendarSettings {
  weekStart: number
  showWeekNumbers: boolean
  defaultEventLength: number
  workingHours: {
    start: string
    end: string
  }
  timeZone: string
}

interface CalendarViewProps {
  events: GoogleCalendarEvent[]
  calendars: CalendarData[]
  colors: CalendarColors
  settings: CalendarSettings
  stats: CalendarStats
  onEventCreate: (eventData: Partial<GoogleCalendarEvent>) => Promise<void>
  onEventUpdate: (eventId: string, eventData: Partial<GoogleCalendarEvent>) => Promise<void>
  onEventDelete: (eventId: string) => Promise<void>
  onCalendarToggle: (calendarId: string) => void
  onEventClick?: (event: GoogleCalendarEvent) => void
  currentDate: Date
  view: 'Month' | 'Week' | 'Day'
  userTimezone?: string
  loading?: boolean
  error?: string | null
  needsConnection?: boolean
  onRetry?: () => void
}

export function CalendarView({ 
  events: passedEvents = [],
  calendars = [],
  colors,
  settings,
  stats,
  onEventCreate,
  onEventUpdate,
  onEventDelete,
  onCalendarToggle,
  onEventClick,
  currentDate: passedCurrentDate,
  view,
  userTimezone,
  loading = false,
  error = null,
  needsConnection = false,
  onRetry
}: CalendarViewProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(passedCurrentDate || new Date())
  const [events, setEvents] = useState<GoogleCalendarEvent[]>(passedEvents)
  const [showDialog, setShowDialog] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedEvent, setSelectedEvent] = useState<GoogleCalendarEvent | null>(null)
  const [newEvent, setNewEvent] = useState<{
    summary: string
    description: string
    startDate: string
    endDate: string
    location: string
    isAllDay: boolean
    addMeet: boolean
    attendees: string
  }>({
    summary: "",
    description: "",
    startDate: new Date().toISOString().slice(0, 16),
    endDate: new Date(Date.now() + 60 * 60 * 1000).toISOString().slice(0, 16),
    location: "",
    isAllDay: false,
    addMeet: false,
    attendees: ""
  })

  // Update local state when props change
  useEffect(() => {
    if (passedEvents) {
      setEvents(passedEvents)
    }
  }, [passedEvents])

  useEffect(() => {
    if (passedCurrentDate) {
      setSelectedDate(passedCurrentDate)
    }
  }, [passedCurrentDate])

  const getEventsForDate = (date: Date) => {
    return events.filter(event => {
      const eventDate = event.start.dateTime ? parseISO(event.start.dateTime) : parseISO(event.start.date!)
      return isSameDay(eventDate, date)
    })
  }

  const getDaysInMonth = () => {
    const start = startOfWeek(startOfMonth(selectedDate))
    const end = endOfWeek(endOfMonth(selectedDate))
    return eachDayOfInterval({ start, end })
  }

  const getWeekDays = () => {
    const start = startOfWeek(selectedDate)
    const end = endOfWeek(selectedDate)
    return eachDayOfInterval({ start, end })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const getEventColor = (event: GoogleCalendarEvent) => {
    if (event.description?.includes('sourceType:ai') || event.summary?.includes('[AI]')) {
      return 'bg-blue-100 border-blue-300 text-blue-800'
    }
    // Default Google Calendar blue
    return 'bg-blue-500 text-white'
  }

  const navigateDate = (direction: 'prev' | 'next') => {
    if (view === 'Month') {
      setSelectedDate(direction === 'prev' ? subMonths(selectedDate, 1) : addMonths(selectedDate, 1))
    } else if (view === 'Week') {
      setSelectedDate(direction === 'prev' ? addDays(selectedDate, -7) : addDays(selectedDate, 7))
    } else {
      setSelectedDate(direction === 'prev' ? addDays(selectedDate, -1) : addDays(selectedDate, 1))
    }
  }

  const renderMonthView = () => {
    const days = getDaysInMonth()
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

    return (
      <div className="bg-white">
        {/* Week day headers */}
        <div className="grid grid-cols-7 border-b border-gray-200">
          {weekDays.map((day) => (
            <div key={day} className="p-3 text-center text-sm font-medium text-gray-500 bg-gray-50">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7">
          {days.map((day, index) => {
            const dayEvents = getEventsForDate(day)
            const isCurrentMonth = isSameMonth(day, selectedDate)
            const isCurrentDay = isToday(day)
            
            return (
              <div
                key={day.toISOString()}
                className={`min-h-32 border-r border-b border-gray-200 p-2 ${
                  !isCurrentMonth ? 'bg-gray-50 text-gray-400' : 'bg-white'
                } ${isCurrentDay ? 'bg-blue-50' : ''}`}
              >
                <div className={`text-sm font-medium mb-1 ${
                  isCurrentDay ? 'text-blue-600' : isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                }`}>
                  {day.getDate()}
                </div>
                
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event) => (
                    <div
                      key={event.id}
                      className={`text-xs p-1 rounded cursor-pointer hover:opacity-80 ${getEventColor(event)}`}
                      onClick={() => onEventClick ? onEventClick(event) : setSelectedEvent(event)}
                    >
                      <div className="truncate">
                        {event.start.dateTime && formatTime(event.start.dateTime)} {event.summary}
                      </div>
                    </div>
                  ))}
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-gray-500 pl-1">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  const renderWeekView = () => {
    const days = getWeekDays()
    const hours = Array.from({ length: 24 }, (_, i) => i)

    return (
      <div className="bg-white">
        {/* Week header */}
        <div className="grid grid-cols-8 border-b border-gray-200">
          <div className="p-3 border-r border-gray-200"></div>
          {days.map((day) => (
            <div key={day.toISOString()} className="p-3 text-center">
              <div className="text-sm font-medium text-gray-500">
                {format(day, 'EEE')}
              </div>
              <div className={`text-lg font-semibold ${
                isToday(day) ? 'text-blue-600' : 'text-gray-900'
              }`}>
                {day.getDate()}
              </div>
            </div>
          ))}
        </div>

        {/* Time slots */}
        <div className="grid grid-cols-8 divide-x divide-gray-200">
          {/* Time column */}
          <div className="border-r border-gray-200">
            {hours.map((hour) => (
              <div key={hour} className="h-16 p-2 text-xs text-gray-500 border-b border-gray-100">
                {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
              </div>
            ))}
          </div>

          {/* Day columns */}
          {days.map((day) => (
            <div key={day.toISOString()} className="relative">
              {hours.map((hour) => (
                <div key={hour} className="h-16 border-b border-gray-100 relative">
                  {/* Events for this hour */}
                  {getEventsForDate(day)
                    .filter(event => {
                      if (!event.start.dateTime) return false
                      const eventHour = new Date(event.start.dateTime).getHours()
                      return eventHour === hour
                    })
                    .map((event) => (
                      <div
                        key={event.id}
                        className={`absolute left-1 right-1 top-1 p-1 rounded text-xs cursor-pointer hover:opacity-80 ${getEventColor(event)}`}
                        onClick={() => onEventClick ? onEventClick(event) : setSelectedEvent(event)}
                      >
                        <div className="truncate font-medium">{event.summary}</div>
                        {event.location && (
                          <div className="truncate opacity-75">{event.location}</div>
                        )}
                      </div>
                    ))}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    )
  }

  const renderDayView = () => {
    const hours = Array.from({ length: 24 }, (_, i) => i)
    const dayEvents = getEventsForDate(selectedDate)

    return (
      <div className="bg-white">
        {/* Day header */}
        <div className="p-4 border-b border-gray-200">
          <div className="text-center">
            <div className="text-sm font-medium text-gray-500">
              {format(selectedDate, 'EEEE')}
            </div>
            <div className={`text-2xl font-semibold ${
              isToday(selectedDate) ? 'text-blue-600' : 'text-gray-900'
            }`}>
              {selectedDate.getDate()}
            </div>
          </div>
        </div>

        {/* Time slots */}
        <div className="divide-y divide-gray-100">
          {hours.map((hour) => (
            <div key={hour} className="flex">
              <div className="w-20 p-2 text-xs text-gray-500 border-r border-gray-200">
                {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
              </div>
              <div className="flex-1 h-16 relative">
                {/* Events for this hour */}
                {dayEvents
                  .filter(event => {
                    if (!event.start.dateTime) return false
                    const eventHour = new Date(event.start.dateTime).getHours()
                    return eventHour === hour
                  })
                  .map((event) => (
                    <div
                      key={event.id}
                      className={`absolute left-2 right-2 top-1 p-2 rounded cursor-pointer hover:opacity-80 ${getEventColor(event)}`}
                      onClick={() => onEventClick ? onEventClick(event) : setSelectedEvent(event)}
                    >
                      <div className="font-medium">{event.summary}</div>
                      {event.location && (
                        <div className="text-sm opacity-75">{event.location}</div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">{error}</p>
          <Button onClick={onRetry} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Calendar Content */}
      <div className="flex-1 overflow-auto">
        {view === 'Month' && renderMonthView()}
        {view === 'Week' && renderWeekView()}
        {view === 'Day' && renderDayView()}
      </div>

      {/* Event Detail Modal */}
      {selectedEvent && (
        <Dialog open={!!selectedEvent} onOpenChange={() => setSelectedEvent(null)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                {selectedEvent.summary}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {selectedEvent.start.dateTime && (
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {format(new Date(selectedEvent.start.dateTime), 'MMM d, yyyy • h:mm a')}
                    {selectedEvent.end.dateTime && 
                      ` - ${format(new Date(selectedEvent.end.dateTime), 'h:mm a')}`
                    }
                  </span>
                </div>
              )}
              
              {selectedEvent.location && (
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">{selectedEvent.location}</span>
                </div>
              )}
              
              {selectedEvent.description && (
                <div className="text-sm text-gray-600">
                  {selectedEvent.description.split('\n').map((line, index) => (
                    <p key={index}>{line}</p>
                  ))}
                </div>
              )}
              
              {selectedEvent.attendees && selectedEvent.attendees.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {selectedEvent.attendees.length} attendee{selectedEvent.attendees.length > 1 ? 's' : ''}
                  </span>
                </div>
              )}
              
              <div className="flex space-x-2 pt-4">
                <Button variant="outline" size="sm" asChild>
                  <a href={selectedEvent.htmlLink} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open in Google Calendar
                  </a>
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}