'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Bell, 
  Video, 
  Repeat,
  X,
  Plus
} from 'lucide-react'
import { GoogleCalendarEvent } from '@/lib/calendar-types'

interface NotificationSetting {
  method: 'email' | 'popup'
  minutes: number
}

export default function NewEventPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Form state
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [location, setLocation] = useState('')
  const [startDate, setStartDate] = useState('')
  const [startTime, setStartTime] = useState('')
  const [endDate, setEndDate] = useState('')
  const [endTime, setEndTime] = useState('')
  const [isAllDay, setIsAllDay] = useState(false)
  const [attendees, setAttendees] = useState<string[]>([])
  const [newAttendee, setNewAttendee] = useState('')
  const [notifications, setNotifications] = useState<NotificationSetting[]>([
    { method: 'popup', minutes: 10 }
  ])
  const [repeatOption, setRepeatOption] = useState('none')
  const [addGoogleMeet, setAddGoogleMeet] = useState(false)
  const [timezone, setTimezone] = useState(Intl.DateTimeFormat().resolvedOptions().timeZone)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize form with URL params if provided
  useEffect(() => {
    const date = searchParams.get('date')
    const time = searchParams.get('time')
    
    if (date) {
      setStartDate(date)
      setEndDate(date)
    }
    
    if (time) {
      setStartTime(time)
      // Set end time to 1 hour later
      const [hours, minutes] = time.split(':').map(Number)
      const endHours = hours + 1
      setEndTime(`${endHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`)
    }
    
    // Set default times if not provided
    if (!date && !time) {
      const now = new Date()
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000)
      
      setStartDate(now.toISOString().split('T')[0])
      setStartTime(now.toTimeString().slice(0, 5))
      setEndDate(oneHourLater.toISOString().split('T')[0])
      setEndTime(oneHourLater.toTimeString().slice(0, 5))
    }
  }, [searchParams])

  const handleAddAttendee = () => {
    if (newAttendee && newAttendee.includes('@') && !attendees.includes(newAttendee)) {
      setAttendees([...attendees, newAttendee])
      setNewAttendee('')
    }
  }

  const handleRemoveAttendee = (email: string) => {
    setAttendees(attendees.filter(a => a !== email))
  }

  const handleAddNotification = () => {
    setNotifications([...notifications, { method: 'popup', minutes: 10 }])
  }

  const handleRemoveNotification = (index: number) => {
    setNotifications(notifications.filter((_, i) => i !== index))
  }

  const handleNotificationChange = (index: number, field: keyof NotificationSetting, value: any) => {
    const updated = [...notifications]
    updated[index] = { ...updated[index], [field]: value }
    setNotifications(updated)
  }

  const handleSave = async () => {
    if (!title.trim()) {
      setError('Event title is required')
      return
    }

    setLoading(true)
    setError(null)
    
    try {
      const eventData: Partial<GoogleCalendarEvent> = {
        summary: title,
        description,
        location,
        start: isAllDay ? {
          date: startDate,
          timeZone: timezone
        } : {
          dateTime: new Date(`${startDate}T${startTime}`).toISOString(),
          timeZone: timezone
        },
        end: isAllDay ? {
          date: endDate || startDate,
          timeZone: timezone
        } : {
          dateTime: new Date(`${endDate}T${endTime}`).toISOString(),
          timeZone: timezone
        },
        attendees: attendees.filter(email => email && email.trim() && email.includes('@')).map(email => ({ email: email.trim() })),
        reminders: {
          useDefault: false,
          overrides: notifications.map(n => ({
            method: n.method,
            minutes: n.minutes
          }))
        }
      }

      // Add conference data if Google Meet is requested
      if (addGoogleMeet) {
        (eventData as any).conferenceData = {
          createRequest: {
            requestId: `meet-${Date.now()}`,
            conferenceSolutionKey: { type: 'hangoutsMeet' }
          }
        }
      }

      const response = await fetch('/api/calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...eventData,
          timeZone: timezone
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create event')
      }

      // Redirect back to calendar
      router.push('/dashboard/calendar')
    } catch (error) {
      console.error('Error saving event:', error)
      setError(error instanceof Error ? error.message : 'Failed to create event')
    } finally {
      setLoading(false)
    }
  }

  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2)
    const minute = i % 2 === 0 ? '00' : '30'
    const time = `${hour.toString().padStart(2, '0')}:${minute}`
    const display = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
    return { value: time, label: display }
  })

  const notificationOptions = [
    { value: 0, label: 'At time of event' },
    { value: 5, label: '5 minutes before' },
    { value: 10, label: '10 minutes before' },
    { value: 15, label: '15 minutes before' },
    { value: 30, label: '30 minutes before' },
    { value: 60, label: '1 hour before' },
    { value: 120, label: '2 hours before' },
    { value: 1440, label: '1 day before' },
    { value: 2880, label: '2 days before' },
    { value: 10080, label: '1 week before' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto p-4 sm:p-6 max-w-6xl">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/dashboard/calendar')}
              className="flex items-center gap-2 hover:bg-white/50 dark:hover:bg-slate-800/50"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Calendar
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                New Event
              </h1>
              <p className="text-sm text-muted-foreground mt-1">
                Create a new calendar event with all the details
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard/calendar')}
              disabled={loading}
              className="flex-1 sm:flex-none"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={loading || !title.trim()}
              className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 flex-1 sm:flex-none"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Calendar className="h-4 w-4" />
                  Save Event
                </>
              )}
            </Button>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg shadow-sm animate-in slide-in-from-top-2 duration-300">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 bg-red-500 rounded-full"></div>
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 lg:gap-8">
          {/* Main Form */}
          <div className="xl:col-span-2 space-y-6">
            {/* Basic Info */}
            <Card className="shadow-lg border-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  Event Details
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Basic information about your event
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-sm font-medium">
                    Event Title <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="title"
                    placeholder="Enter event title..."
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className={`text-lg h-12 transition-all duration-200 ${
                      !title.trim() && error ? 'border-red-300 focus:border-red-500' : 'focus:border-blue-500'
                    }`}
                  />
                  {!title.trim() && (
                    <p className="text-xs text-muted-foreground">
                      A title is required for your event
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-medium">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="Add a detailed description of your event..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={4}
                    className="resize-none transition-all duration-200 focus:border-blue-500"
                  />
                  <p className="text-xs text-muted-foreground">
                    Optional: Provide additional details about the event
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location" className="text-sm font-medium">
                    Location
                  </Label>
                  <Input
                    id="location"
                    placeholder="Enter location or meeting room..."
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="h-11 transition-all duration-200 focus:border-blue-500"
                  />
                  <p className="text-xs text-muted-foreground">
                    Physical location or virtual meeting details
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Date & Time */}
            <Card className="shadow-lg border-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <Clock className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  Date & Time
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  When will your event take place?
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-3 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                  <Checkbox
                    id="all-day"
                    checked={isAllDay}
                    onCheckedChange={(checked) => setIsAllDay(checked === true)}
                    className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  />
                  <Label htmlFor="all-day" className="text-sm font-medium cursor-pointer">
                    All day event
                  </Label>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Start Date</Label>
                    <Input
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      className="h-11 transition-all duration-200 focus:border-blue-500"
                    />
                  </div>
                  {!isAllDay && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Start Time</Label>
                      <Select value={startTime} onValueChange={setStartTime}>
                        <SelectTrigger className="h-11 transition-all duration-200 focus:border-blue-500">
                          <SelectValue placeholder="Select start time" />
                        </SelectTrigger>
                        <SelectContent>
                          {timeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">End Date</Label>
                    <Input
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className="h-11 transition-all duration-200 focus:border-blue-500"
                    />
                  </div>
                  {!isAllDay && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">End Time</Label>
                      <Select value={endTime} onValueChange={setEndTime}>
                        <SelectTrigger className="h-11 transition-all duration-200 focus:border-blue-500">
                          <SelectValue placeholder="Select end time" />
                        </SelectTrigger>
                        <SelectContent>
                          {timeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Repeat</Label>
                  <Select value={repeatOption} onValueChange={setRepeatOption}>
                    <SelectTrigger className="h-11 transition-all duration-200 focus:border-blue-500">
                      <SelectValue placeholder="Select repeat option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Does not repeat</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Choose how often this event should repeat
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Attendees */}
            <Card className="shadow-lg border-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  Attendees
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Invite people to your event
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter attendee email address..."
                    value={newAttendee}
                    onChange={(e) => setNewAttendee(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddAttendee()}
                    className="h-11 transition-all duration-200 focus:border-blue-500"
                  />
                  <Button
                    onClick={handleAddAttendee}
                    size="sm"
                    className="h-11 px-4 bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {attendees.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">
                      Invited ({attendees.length})
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {attendees.map((email) => (
                        <Badge
                          key={email}
                          variant="secondary"
                          className="flex items-center gap-2 px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800"
                        >
                          {email}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveAttendee(email)}
                            className="h-4 w-4 p-0 hover:bg-blue-100 dark:hover:bg-blue-800/50 rounded-full"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {attendees.length === 0 && (
                  <p className="text-xs text-muted-foreground text-center py-4 border-2 border-dashed border-slate-200 dark:border-slate-700 rounded-lg">
                    No attendees added yet. Enter email addresses above to invite people.
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Google Meet */}
            <Card className="shadow-lg border-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                    <Video className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  Conference
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                  <Checkbox
                    id="google-meet"
                    checked={addGoogleMeet}
                    onCheckedChange={(checked) => setAddGoogleMeet(checked === true)}
                    className="data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600"
                  />
                  <div>
                    <Label htmlFor="google-meet" className="text-sm font-medium cursor-pointer">
                      Add Google Meet
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Automatically create a video meeting link
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Notifications */}
            <Card className="shadow-lg border-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                    <Bell className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  Notifications
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Get reminded about your event
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {notifications.map((notification, index) => (
                  <div key={index} className="flex items-center gap-2 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                    <Select
                      value={notification.method}
                      onValueChange={(value) => handleNotificationChange(index, 'method', value)}
                    >
                      <SelectTrigger className="w-24 h-9">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="popup">Popup</SelectItem>
                        <SelectItem value="email">Email</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select
                      value={notification.minutes.toString()}
                      onValueChange={(value) => handleNotificationChange(index, 'minutes', parseInt(value))}
                    >
                      <SelectTrigger className="flex-1 h-9">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {notificationOptions.map(option => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveNotification(index)}
                      className="h-9 w-9 p-0 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddNotification}
                  className="w-full h-10 border-dashed border-2 hover:bg-blue-50 dark:hover:bg-blue-900/30 hover:border-blue-300 dark:hover:border-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Notification
                </Button>
              </CardContent>
            </Card>

            {/* Timezone */}
            <Card className="shadow-lg border-0 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                    <Clock className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  Time Zone
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                  <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    {timezone}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Events will be created in this timezone
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const now = new Date()
                    const tomorrow = new Date(now)
                    tomorrow.setDate(tomorrow.getDate() + 1)
                    setStartDate(tomorrow.toISOString().split('T')[0])
                    setEndDate(tomorrow.toISOString().split('T')[0])
                  }}
                  className="w-full justify-start"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Set for Tomorrow
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setStartTime('09:00')
                    setEndTime('10:00')
                  }}
                  className="w-full justify-start"
                >
                  <Clock className="h-4 w-4 mr-2" />
                  9:00 AM - 10:00 AM
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setAddGoogleMeet(true)
                    setNotifications([{ method: 'popup', minutes: 10 }])
                  }}
                  className="w-full justify-start"
                >
                  <Video className="h-4 w-4 mr-2" />
                  Meeting Setup
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}